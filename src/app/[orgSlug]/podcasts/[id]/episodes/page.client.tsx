"use client";

import { useQuery } from "@tanstack/react-query";
import {
  ChevronDown,
  ChevronRight,
  CloudUpload,
  Plus,
  Trash2,
} from "lucide-react";
import { useParams } from "next/navigation";
import { type FC, useState } from "react";
import { OrgAwareLink } from "@/app/organization-aware-link";
import { QueryHandler } from "@/components/query-handler";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { useTRPC } from "@/server/trpc/client";

export const PodcastEpisodesClientPage: FC = () => {
  const params = useParams<{ id: string }>();
  const trpc = useTRPC();

  const [openSeasons, setOpenSeasons] = useState<Set<number>>(new Set());

  const toggleSeason = (index: number) => {
    setOpenSeasons((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  const podcastQuery = useQuery(
    trpc.podcasts.getSingleWithSeasons.queryOptions({ id: params.id }),
  );

  return (
    <>
      <section className="flex items-end justify-between">
        <div className="space-y-4">
          <div className="space-y-2">
            <h1 className="text-4xl font-semibold tracking-tight">
              Seasons & Episodes
            </h1>
            <p className="text-muted-foreground text-lg">
              Manage your seasons and episodes here.
            </p>
          </div>
        </div>
        <div className="flex gap-4">
          <Button size="sm" asChild>
            <OrgAwareLink href={"/podcasts/new"}>
              <Plus />
              New Season
            </OrgAwareLink>
          </Button>
        </div>
      </section>
      <section className="py-12">
        <QueryHandler
          query={podcastQuery}
          render={({ data }) =>
            data && (
              <div className="grid gap-4">
                {data.seasons.map((season, seasonIndex) => (
                  <Collapsible
                    key={season.id}
                    open={openSeasons.has(seasonIndex)}
                    onOpenChange={() => toggleSeason(seasonIndex)}
                  >
                    <Card className="py-0 overflow-hidden bg-background">
                      <CollapsibleTrigger asChild>
                        <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors py-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3 flex-1">
                              {openSeasons.has(seasonIndex) ? (
                                <ChevronDown className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                              ) : (
                                <ChevronRight className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                              )}
                              {season.image ? (
                                <div>
                                  {/** biome-ignore lint/performance/noImgElement: gonna be really dynamic from where the images come so we dont need to optimize */}
                                  <img
                                    src={season.image}
                                    alt={season.name}
                                    width={64}
                                    height={64}
                                    className="object-cover rounded-md"
                                  />
                                </div>
                              ) : (
                                <div className="h-16 w-16 rounded-md bg-muted flex-shrink-0" />
                              )}
                              <div className="flex-1 min-w-0">
                                <CardTitle className="text-base truncate">
                                  {season.name}
                                </CardTitle>
                                <CardDescription className="text-sm">
                                  {season.episodes.length} episode
                                  {season.episodes.length !== 1 ? "s" : ""}
                                </CardDescription>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="secondary">
                                {season.episodes.length}
                              </Badge>
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={(e) => {
                                  e.stopPropagation();
                                }}
                                className="h-8 w-8"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                      </CollapsibleTrigger>
                      <CollapsibleContent className="pb-4">
                        <CardContent className="pt-0 space-y-4">
                          <div className="space-y-2 pt-2">
                            <h4 className="text-sm font-semibold">Episodes</h4>
                            {season.episodes.map((episode) => (
                              <Card
                                className="bg-muted/30 py-0"
                                key={episode.id}
                              >
                                <CardHeader className="py-4 gap-0">
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2 flex-1 min-w-0">
                                      {episode.image ? (
                                        <div>
                                          {/** biome-ignore lint/performance/noImgElement: gonna be really dynamic from where the images come so we dont need to optimize */}
                                          <img
                                            src={episode.image}
                                            alt={episode.name}
                                            width={32}
                                            height={32}
                                            className="object-cover rounded-md"
                                          />
                                        </div>
                                      ) : (
                                        <div className="h-8 w-8 rounded-md bg-muted flex-shrink-0" />
                                      )}
                                      <span className="text-sm font-medium truncate">
                                        {episode.name}
                                      </span>
                                    </div>
                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="icon"
                                      className="h-6 w-6 flex-shrink-0"
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </CardHeader>
                              </Card>
                            ))}
                          </div>
                        </CardContent>
                      </CollapsibleContent>
                    </Card>
                  </Collapsible>
                ))}
              </div>
            )
          }
        />
      </section>
    </>
  );
};
