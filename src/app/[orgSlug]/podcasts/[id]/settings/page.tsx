import type { NextPage } from "next";
import { PodcastDeleteCard } from "./components/podcast-delete-card";
import { PodcastNameCard } from "./components/podcast-name-card";
import { PodcastSubdomainCard } from "./components/podcast-subdomain-card";

const PodcastSettingsPage: NextPage = () => {
  return (
    <section className="flex w-full flex-col gap-4 md:gap-6">
      <PodcastNameCard />
      <PodcastSubdomainCard />
      <PodcastDeleteCard />
    </section>
  );
};

export default PodcastSettingsPage;
