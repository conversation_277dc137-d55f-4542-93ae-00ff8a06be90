"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import { type FC, useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import type z from "zod";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { useTRPC } from "@/server/trpc/client";
import { podcastFormSchema } from "../../../new/page";

const formSchema = podcastFormSchema.pick({ name: true });

export const PodcastNameCard: FC = () => {
  const trpc = useTRPC();
  const params = useParams<{ orgSlug: string; id: string }>();

  const { data: podcast, isLoading } = useQuery(
    trpc.podcasts.getSingle.queryOptions({ id: params.id }),
  );

  const { mutateAsync: updatePodcast, isPending: isUpdating } = useMutation(
    trpc.podcasts.update.mutationOptions(),
  );

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: { name: podcast?.name || "" },
  });

  useEffect(() => {
    if (!isLoading) form.reset({ name: podcast?.name || "" });
  }, [isLoading, podcast, form]);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    await updatePodcast(
      { id: params.id, data: values },
      {
        onSuccess: () => {
          toast.success("Podcast updated successfully");
        },
        onError: () => {
          toast.error("Failed to update podcast");
        },
      },
    );
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <fieldset disabled={isUpdating}>
          <Card className="pb-0">
            <CardHeader>
              <CardTitle className="font-semibold text-lg md:text-xl">
                Podcast Name
              </CardTitle>
              <CardDescription>Change your podcast name</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-9 w-full" />
              ) : (
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input placeholder="My Awesome Podcast" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </CardContent>
            <CardFooter className="items-center px-6 !py-4 border-t md:flex-row [.border-t]:pt-6 flex flex-col justify-between gap-4 rounded-b-xl">
              <CardDescription>
                Use minimum 3 characters and maximum 64 characters.
              </CardDescription>
              {isLoading ? (
                <Skeleton className="h-8 w-14" />
              ) : (
                <Button type="submit" className="md:ms-auto" size="sm">
                  Save
                </Button>
              )}
            </CardFooter>
          </Card>
        </fieldset>
      </form>
    </Form>
  );
};
