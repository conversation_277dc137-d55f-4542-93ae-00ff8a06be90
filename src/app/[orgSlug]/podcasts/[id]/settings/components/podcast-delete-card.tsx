"use client";

import { useMutation } from "@tanstack/react-query";
import { useParams, useRouter } from "next/navigation";
import type { FC } from "react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useTRPC } from "@/server/trpc/client";

export const PodcastDeleteCard: FC = () => {
  const router = useRouter();
  const trpc = useTRPC();
  const params = useParams<{ orgSlug: string; id: string }>();

  const { mutateAsync: deletePodcast, isPending: isDeleting } = useMutation(
    trpc.podcasts.delete.mutationOptions(),
  );

  const onDelete = async () => {
    await deletePodcast(
      { id: params.id },
      {
        onSuccess: () => {
          router.push(`/${params.orgSlug}/podcasts`);
          toast.success("Podcast deleted successfully");
        },
        onError: () => {
          toast.error("Failed to delete podcast");
        },
      },
    );
  };

  return (
    <Card className="pb-0">
      <CardHeader>
        <CardTitle className="font-semibold text-lg md:text-xl">
          Delete Podcast
        </CardTitle>
        <CardDescription>
          Deleting a podcast is permanent and cannot be undone.
        </CardDescription>
      </CardHeader>
      <CardFooter className="items-center px-6 !py-4 border border-destructive bg-destructive/15 md:flex-row [.border-t]:pt-6 flex flex-col justify-between gap-4 rounded-b-xl">
        <CardDescription className="text-destructive">
          This will delete the podcast and all its episodes.
        </CardDescription>
        <Button
          variant={"destructive"}
          className="md:ms-auto"
          size="sm"
          onClick={onDelete}
          disabled={isDeleting}
        >
          Delete
        </Button>
      </CardFooter>
    </Card>
  );
};
