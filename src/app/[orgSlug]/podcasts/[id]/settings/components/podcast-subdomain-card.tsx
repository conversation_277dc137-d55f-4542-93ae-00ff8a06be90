"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import { type FC, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import type z from "zod";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import {
  InputGroup,
  InputGroupAddon,
  InputGroupInput,
} from "@/components/ui/input-group";
import { Skeleton } from "@/components/ui/skeleton";
import { Spinner } from "@/components/ui/spinner";
import { useTRPC } from "@/server/trpc/client";
import { podcastFormSchema } from "../../../new/schema";

const formSchema = podcastFormSchema.pick({ subdomain: true });

const normalizeSubdomain = (raw: string) =>
  raw
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9-]/g, "-")
    .replace(/-{2,}/g, "-")
    .replace(/^-+|-+$/g, "");

// Tiny debounce hook
function useDebounced<T>(value: T, delay = 350) {
  const [debounced, setDebounced] = useState(value);
  useEffect(() => {
    const t = setTimeout(() => setDebounced(value), delay);
    return () => clearTimeout(t);
  }, [value, delay]);
  return debounced;
}

const ROOT_DOMAIN = process.env.NEXT_PUBLIC_ROOT_DOMAIN ?? "example.com"; // e.g. podcasts.yourapp.com

export const PodcastSubdomainCard: FC = () => {
  const trpc = useTRPC();
  const params = useParams<{ orgSlug: string; id: string }>();

  const { data: podcast, isLoading } = useQuery(
    trpc.podcasts.getSingle.queryOptions({ id: params.id }),
  );

  const { mutateAsync: updatePodcast, isPending: isUpdating } = useMutation(
    trpc.podcasts.update.mutationOptions(),
  );

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: { subdomain: podcast?.subdomain || "" },
    mode: "onChange",
  });

  useEffect(() => {
    if (!isLoading) form.reset({ subdomain: podcast?.subdomain || "" });
  }, [isLoading, podcast, form]);

  // Keep subdomain normalized as user types
  const rawValue = form.watch("subdomain");
  const normalized = useMemo(
    () => normalizeSubdomain(rawValue ?? ""),
    [rawValue],
  );

  useEffect(() => {
    // If user types invalid chars, rewrite silently
    if (rawValue !== normalized) {
      form.setValue("subdomain", normalized, {
        shouldDirty: true,
        shouldValidate: true,
      });
    }
  }, [normalized]);

  const debounced = useDebounced(normalized, 350);

  // Live availability check (skip if empty or same as current)
  const shouldCheck =
    debounced.length >= 3 && debounced !== (podcast?.subdomain ?? "");

  const availabilityQuery = useQuery(
    trpc.podcasts.subdomainAvailability.queryOptions({
      subdomain: debounced || "",
      excludeId: params.id,
    }),
  );

  const status:
    | "idle"
    | "checking"
    | "available"
    | "taken"
    | "invalid"
    | "yours" = (() => {
    if (!normalized) return "idle";
    if (normalized === podcast?.subdomain) return "yours";
    if (availabilityQuery.isFetching) return "checking";
    const res = availabilityQuery.data;
    if (!shouldCheck) return normalized.length < 3 ? "invalid" : "idle";
    if (!res) return "checking";
    if (res.available) return "available";
    return res.reason === "taken" || res.reason === "reserved"
      ? "taken"
      : "invalid";
  })();

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    await updatePodcast(
      {
        id: params.id,
        data: { subdomain: normalizeSubdomain(values.subdomain) },
      },
      {
        onSuccess: () => {
          toast.success("Podcast updated successfully");
        },
        onError: () => {
          toast.error("Failed to update podcast");
        },
      },
    );
  };

  const hint = (() => {
    if (status === "yours") return "This is your current subdomain.";
    if (status === "available")
      return `${normalized}.${ROOT_DOMAIN} is available!`;
    if (status === "taken")
      return `${normalized}.${ROOT_DOMAIN} is not available.`;
    if (status === "invalid")
      return "Use 3–64 characters: a–z, 0–9, and hyphens.";
    return "Use minimum 3 characters and maximum 64 characters.";
  })();

  const disabled =
    isUpdating ||
    status === "checking" ||
    status === "invalid" ||
    status === "taken";

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <fieldset disabled={isUpdating}>
          <Card className="pb-0">
            <CardHeader>
              <CardTitle className="font-semibold text-lg md:text-xl">
                Subdomain
              </CardTitle>
              <CardDescription>
                Choose how your podcast is reached.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-9 w-full" />
              ) : (
                <FormField
                  control={form.control}
                  name="subdomain"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <InputGroup>
                          <InputGroupInput
                            {...field}
                            placeholder="my-podcast"
                          />
                          <InputGroupAddon>https://</InputGroupAddon>
                          <InputGroupAddon align="inline-end">
                            .{ROOT_DOMAIN}
                          </InputGroupAddon>
                        </InputGroup>
                      </FormControl>
                      <div className="mt-2 flex items-center gap-2">
                        <Badge
                          variant={
                            status === "available"
                              ? "default"
                              : status === "taken"
                                ? "destructive"
                                : "outline"
                          }
                        >
                          {status === "checking" ? (
                            <>
                              <Spinner className="mr-1 size-2" />
                              Checking
                            </>
                          ) : status === "available" ? (
                            "Available"
                          ) : status === "taken" ? (
                            "Unavailable"
                          ) : status === "yours" ? (
                            "Current"
                          ) : (
                            <>
                              <Spinner className="mr-1 size-2" />
                              Checking
                            </>
                          )}
                        </Badge>
                        <CardDescription>{hint}</CardDescription>
                      </div>
                      {availabilityQuery.data?.suggestions?.length &&
                      status === "taken" ? (
                        <div className="mt-3 flex flex-wrap gap-2">
                          {availabilityQuery.data.suggestions.map((s) => (
                            <button
                              key={s}
                              type="button"
                              onClick={() =>
                                form.setValue("subdomain", s, {
                                  shouldDirty: true,
                                  shouldValidate: true,
                                })
                              }
                              className="text-sm px-2 py-1 rounded-md border hover:bg-accent"
                            >
                              {s}
                            </button>
                          ))}
                        </div>
                      ) : null}
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </CardContent>
            <CardFooter className="items-center px-6 !py-4 border-t md:flex-row [.border-t]:pt-6 flex flex-col justify-between gap-4 rounded-b-xl">
              <CardDescription>
                Subdomains can include letters, numbers, and hyphens.
              </CardDescription>
              {isLoading ? (
                <Skeleton className="h-8 w-14" />
              ) : (
                <Button
                  type="submit"
                  className="md:ms-auto"
                  size="sm"
                  disabled={disabled}
                >
                  {isUpdating ? (
                    <span className="inline-flex items-center gap-2">
                      <Spinner />
                      Saving
                    </span>
                  ) : (
                    "Save"
                  )}
                </Button>
              )}
            </CardFooter>
          </Card>
        </fieldset>
      </form>
    </Form>
  );
};
