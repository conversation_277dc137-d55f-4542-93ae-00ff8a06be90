"use client";

import { useQuery } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import type { FC } from "react";
import { QueryHandler } from "@/components/query-handler";
import { useTRPC } from "@/server/trpc/client";

export const PodcastDetailClientPage: FC = () => {
  const params = useParams<{ id: string }>();
  const trpc = useTRPC();

  const podcastQuery = useQuery(
    trpc.podcasts.getSingle.queryOptions({ id: params.id }),
  );

  return (
    <QueryHandler
      query={podcastQuery}
      render={({ data }) => data && <section>{data.name}</section>}
    />
  );
};
