"use client";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@radix-ui/react-collapsible";
import { ChevronDown, ChevronRight, Trash2 } from "lucide-react";
import type { FC } from "react";
import { type UseFormReturn, useFieldArray } from "react-hook-form";
import { RTE } from "@/components/rte";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import type { PodcastFormValues } from "../schema";
import { EpisodeFormSection } from "./episode-form-section";

interface ISeasonFormSectionProps {
  seasonIndex: number;
  form: UseFormReturn<PodcastFormValues>;
  isOpen: boolean;
  onToggle: () => void;
  onRemove: () => void;
}

export const SeasonFormSection: FC<ISeasonFormSectionProps> = ({
  seasonIndex,
  form,
  isOpen,
  onToggle,
  onRemove,
}) => {
  const { fields: episodeFields, remove: removeEpisode } = useFieldArray({
    control: form.control,
    name: `seasons.${seasonIndex}.episodes`,
  });

  return (
    <Collapsible open={isOpen} onOpenChange={onToggle}>
      <Card className="py-0 overflow-hidden">
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-muted transition-colors py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 flex-1">
                {isOpen ? (
                  <ChevronDown className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                ) : (
                  <ChevronRight className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                )}
                {form.watch(`seasons.${seasonIndex}.image`) ? (
                  <div>
                    {/** biome-ignore lint/performance/noImgElement: gonna be really dynamic from where the images come so we dont need to optimize */}
                    <img
                      src={form.watch(`seasons.${seasonIndex}.image`) || ""}
                      alt={form.watch(`seasons.${seasonIndex}.name`) || ""}
                      width={64}
                      height={64}
                      className="object-cover rounded-md"
                    />
                  </div>
                ) : (
                  <div className="h-16 w-16 rounded-md bg-muted flex-shrink-0" />
                )}
                <div className="flex-1 min-w-0">
                  <CardTitle className="text-base truncate">
                    {form.watch(`seasons.${seasonIndex}.name`) ||
                      `Season ${seasonIndex + 1}`}
                  </CardTitle>
                  <CardDescription className="text-sm">
                    {episodeFields.length} episode
                    {episodeFields.length !== 1 ? "s" : ""}
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{episodeFields.length}</Badge>
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation();
                    onRemove();
                  }}
                  className="h-8 w-8"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent className="pb-4">
          <CardContent className="pt-0 space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name={`seasons.${seasonIndex}.name`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Season Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Season 1" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`seasons.${seasonIndex}.image`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Season Image URL</FormLabel>
                    <FormControl>
                      <Input
                        type="url"
                        placeholder="https://example.com/season.jpg"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name={`seasons.${seasonIndex}.description`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Season Description</FormLabel>
                  <FormControl>
                    <RTE
                      html={field.value ?? ""}
                      onChange={(html) => field.onChange(html)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-2 pt-2">
              <h4 className="text-sm font-semibold">Episodes</h4>
              {episodeFields.map((episode, episodeIndex) => (
                <EpisodeFormSection
                  key={episode.id}
                  seasonIndex={seasonIndex}
                  episodeIndex={episodeIndex}
                  form={form}
                  onRemove={() => removeEpisode(episodeIndex)}
                />
              ))}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Card>
    </Collapsible>
  );
};
