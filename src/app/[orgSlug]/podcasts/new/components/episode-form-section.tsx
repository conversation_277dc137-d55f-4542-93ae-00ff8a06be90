"use client";

import { ChevronDown, ChevronR<PERSON>, Trash2 } from "lucide-react";
import { type FC, useState } from "react";
import type { UseFormReturn } from "react-hook-form";
import { RTE } from "@/components/rte";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import type { PodcastFormValues } from "../schema";

interface IEpisodeFormSectionProps {
  seasonIndex: number;
  episodeIndex: number;
  form: UseFormReturn<PodcastFormValues>;
  onRemove: () => void;
}

export const EpisodeFormSection: FC<IEpisodeFormSectionProps> = ({
  seasonIndex,
  episodeIndex,
  form,
  onRemove,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="bg-muted/30 py-0">
      <CardHeader className="py-4 gap-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-6 w-6 flex-shrink-0"
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
            {form.watch(
              `seasons.${seasonIndex}.episodes.${episodeIndex}.image`,
            ) ? (
              <div>
                {/** biome-ignore lint/performance/noImgElement: gonna be really dynamic from where the images come so we dont need to optimize */}
                <img
                  src={
                    form.watch(
                      `seasons.${seasonIndex}.episodes.${episodeIndex}.image`,
                    ) || ""
                  }
                  alt={
                    form.watch(
                      `seasons.${seasonIndex}.episodes.${episodeIndex}.name`,
                    ) || ""
                  }
                  width={32}
                  height={32}
                  className="object-cover rounded-md"
                />
              </div>
            ) : (
              <div className="h-8 w-8 rounded-md bg-muted flex-shrink-0" />
            )}
            <span className="text-sm font-medium truncate">
              {form.watch(
                `seasons.${seasonIndex}.episodes.${episodeIndex}.name`,
              ) || `Episode ${episodeIndex + 1}`}
            </span>
          </div>
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={onRemove}
            className="h-6 w-6 flex-shrink-0"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>
      {isExpanded && (
        <CardContent className="pt-0 pb-3 space-y-3">
          <FormField
            control={form.control}
            name={`seasons.${seasonIndex}.episodes.${episodeIndex}.name`}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xs">Episode Name</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Episode title"
                    {...field}
                    className="h-8 text-sm"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name={`seasons.${seasonIndex}.episodes.${episodeIndex}.description`}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xs">Description</FormLabel>
                <FormControl>
                  <RTE
                    html={field.value ?? ""}
                    onChange={(html) => field.onChange(html)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid gap-3 md:grid-cols-2">
            <FormField
              control={form.control}
              name={`seasons.${seasonIndex}.episodes.${episodeIndex}.image`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-xs">Image URL</FormLabel>
                  <FormControl>
                    <Input
                      type="url"
                      placeholder="https://..."
                      {...field}
                      className="h-8 text-sm"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`seasons.${seasonIndex}.episodes.${episodeIndex}.duration`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-xs">Duration</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="45:30"
                      {...field}
                      className="h-8 text-sm"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name={`seasons.${seasonIndex}.episodes.${episodeIndex}.audio`}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-xs">Audio URL</FormLabel>
                <FormControl>
                  <Input
                    type="url"
                    placeholder="https://example.com/episode.mp3"
                    {...field}
                    className="h-8 text-sm"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      )}
    </Card>
  );
};
