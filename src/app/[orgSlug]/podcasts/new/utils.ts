import { $generateHtmlFromNodes } from "@lexical/html";
import { LinkNode } from "@lexical/link";
import {
  $isEditorState,
  createEditor,
  type EditorState,
  type SerializedEditorState,
  type SerializedLexicalNode,
} from "lexical";

export const isSerializedLexical = (
  v: unknown,
): v is SerializedEditorState<SerializedLexicalNode> =>
  !!v &&
  typeof v === "object" &&
  (v as any).root?.type === "root" &&
  typeof (v as any).root?.version === "number";

export function serializedLexicalToHTML(
  serialized: string | SerializedEditorState<SerializedLexicalNode>,
): string {
  const temp = createEditor({
    namespace: "html-export",
    nodes: [LinkNode],
  });
  const state = temp.parseEditorState(serialized);
  temp.setEditorState(state);
  let html = "";
  temp.getEditorState().read(() => {
    html = $generateHtmlFromNodes(temp);
  });
  return html;
}

export function editorStateToHTML(state: EditorState): string {
  if (typeof state?.toJSON === "function") {
    return serializedLexicalToHTML(state.toJSON());
  }

  return "";
}

export function normalizeDescription(desc: unknown): string | undefined {
  if ($isEditorState(desc)) return editorStateToHTML(desc);
  if (isSerializedLexical(desc)) return serializedLexicalToHTML(desc);
  if (typeof desc === "string") return desc; // already a string; keep it
  return undefined; // preserve undefined/null
}

export function descriptionToHtml(desc: unknown): string {
  if ($isEditorState(desc)) return editorStateToHTML(desc);
  if (isSerializedLexical(desc)) return serializedLexicalToHTML(desc);
  if (typeof desc === "string") return desc;
  return "";
}
