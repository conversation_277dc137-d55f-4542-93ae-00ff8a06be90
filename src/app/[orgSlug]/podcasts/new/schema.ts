import { z } from "zod";

const SerializedLexicalState = z
  .object({ root: z.object({ type: z.literal("root") }).passthrough() })
  .passthrough();

const DescriptionField = z
  .union([z.string(), SerializedLexicalState])
  .optional();

const episodeSchema = z.object({
  name: z.string().min(1, "Episode name is required"),
  description: DescriptionField,
  image: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  audio: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  duration: z.string().optional(),
});

const seasonSchema = z.object({
  name: z.string().min(1, "Season name is required"),
  description: DescriptionField,
  image: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  episodes: z.array(episodeSchema),
});

export const podcastFormSchema = z.object({
  name: z.string().min(1, "Podcast name is required"),
  subdomain: z.string().optional(),
  description: DescriptionField,
  image: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  email: z.string().email("Must be a valid email").optional().or(z.literal("")),
  owner: z.string().optional(),
  copyright: z.string().optional(),
  seasons: z.array(seasonSchema),
});

export type PodcastFormValues = z.infer<typeof podcastFormSchema>;
