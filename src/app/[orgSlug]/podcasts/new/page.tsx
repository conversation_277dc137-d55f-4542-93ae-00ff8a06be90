"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { Loader2, Upload } from "lucide-react";
import type { NextPage } from "next";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { toast } from "sonner";
import { RTE } from "@/components/rte";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  InputGroup,
  InputGroupAddon,
  InputGroupInput,
  InputGroupText,
} from "@/components/ui/input-group";
import { Label } from "@/components/ui/label";
import { slugify } from "@/lib/utils";
import { useTRPC } from "@/server/trpc/client";
import { SeasonFormSection } from "./components/season-form-section";
import { type PodcastFormValues, podcastFormSchema } from "./schema";
import { descriptionToHtml, normalizeDescription } from "./utils";

const PodcastNewPage: NextPage = () => {
  const trpc = useTRPC();
  const router = useRouter();

  const [rssUrl, setRssUrl] = useState("");
  const [isImporting, setIsImporting] = useState(false);
  const [openSeasons, setOpenSeasons] = useState<Set<number>>(new Set());

  const { mutateAsync: parseRSS } = useMutation(
    trpc.parse.parseRSS.mutationOptions(),
  );

  const { mutateAsync: createPodcast } = useMutation(
    trpc.podcasts.create.mutationOptions(),
  );

  const form = useForm<PodcastFormValues>({
    resolver: zodResolver(podcastFormSchema),
    defaultValues: {
      name: "",
      subdomain: "",
      description: "",
      image: "",
      email: "",
      owner: "",
      copyright: "",
      seasons: [],
    },
  });

  const { fields: seasonFields, remove: removeSeason } = useFieldArray({
    control: form.control,
    name: "seasons",
  });

  const handleImportFromRss = async () => {
    if (!rssUrl.trim()) {
      toast.error("Please enter a valid RSS feed URL");
      return;
    }

    setIsImporting(true);
    try {
      const { data } = await parseRSS({ url: rssUrl }, {});

      form.reset({
        name: data.title || "",
        subdomain: slugify(data.title.toLowerCase()) || "",
        description: data.description || "",
        image: data.image || "",
        email: data.email || "",
        owner: data.owner || "",
        copyright: data.copyright || "",
        seasons: data.seasons || [],
      });

      if (data.seasons && data.seasons.length > 0) {
        setOpenSeasons(new Set([0]));
      }

      toast.success("RSS feed imported successfully");
      // setRssUrl("");
    } catch (error) {
      console.error(error);
      toast.error("Failed to import RSS feed");
    } finally {
      setIsImporting(false);
    }
  };

  const onSubmit = async (values: PodcastFormValues) => {
    const apiValues = {
      ...values,
      description: normalizeDescription(values.description),
      seasons: values.seasons.map((season) => ({
        ...season,
        description: normalizeDescription(season.description),
        episodes: season.episodes.map((ep) => ({
          ...ep,
          description: normalizeDescription(ep.description),
        })),
      })),
    };

    toast.promise(new Promise((resolve) => setTimeout(resolve, 2000)), {
      loading: "Creating podcast...",
      success: "Podcast created successfully",
      error: "Failed to create podcast",
    });
  };

  const toggleSeason = (index: number) => {
    setOpenSeasons((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  return (
    <>
      <section className="flex items-end justify-between">
        <div className="space-y-4">
          <div className="space-y-2">
            <h1 className="text-4xl font-semibold tracking-tight">
              Create Podcast
            </h1>
            <p className="text-muted-foreground text-lg">
              Create a new podcast here. Or comfortably import by passing a feed
              URL.
            </p>
          </div>
        </div>
      </section>
      <section className="py-12">
        <div className="grid md:grid-cols-2 gap-12">
          <div className="space-y-2">
            <Label htmlFor="rss-url">RSS Feed URL</Label>
            <div className="flex gap-2">
              <Input
                id="rss-url"
                type="url"
                placeholder="https://example.com/podcast/feed.xml"
                value={rssUrl}
                onChange={(e) => setRssUrl(e.target.value)}
                className="flex-1"
              />
              <Button
                type="button"
                onClick={handleImportFromRss}
                disabled={isImporting}
              >
                {isImporting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Importing
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Import
                  </>
                )}
              </Button>
            </div>
            <p className="text-muted-foreground text-sm">
              Enter your podcast RSS feed URL to automatically populate the form
              fields
            </p>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="mt-6">
            <div className="grid md:grid-cols-2 gap-12">
              <div>
                <div className="sticky top-64 space-y-6">
                  <div className="grid gap-6 md:grid-cols-2 items-start">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Podcast Name{" "}
                            <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="My Awesome Podcast"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="subdomain"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Subdomain</FormLabel>
                          <FormControl>
                            <InputGroup>
                              <InputGroupInput
                                placeholder="my-podcast"
                                {...field}
                              />
                              <InputGroupAddon align="inline-end">
                                <InputGroupText>.puka.cloud</InputGroupText>
                              </InputGroupAddon>
                            </InputGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <RTE
                            html={descriptionToHtml(field.value)}
                            onChange={(html) => field.onChange(html)} // editor -> HTML
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="image"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cover Image URL</FormLabel>
                        <FormControl>
                          <Input
                            type="url"
                            placeholder="https://example.com/cover.jpg"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid gap-6 md:grid-cols-3">
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Contact Email</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="<EMAIL>"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="owner"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Owner</FormLabel>
                          <FormControl>
                            <Input placeholder="John Doe" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="copyright"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Copyright</FormLabel>
                          <FormControl>
                            <Input placeholder="© 2025 My Podcast" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="flex justify-end gap-3 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => router.back()}
                    >
                      Cancel
                    </Button>
                    <Button type="submit">Create Podcast</Button>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                {seasonFields.length > 0 && (
                  <div className="space-y-4 pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold">
                          Seasons & Episodes
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          {seasonFields.length} season
                          {seasonFields.length !== 1 ? "s" : ""} with{" "}
                          {seasonFields.reduce(
                            (acc, s) => acc + s.episodes.length,
                            0,
                          )}{" "}
                          episode
                          {seasonFields.reduce(
                            (acc, s) => acc + s.episodes.length,
                            0,
                          ) !== 1
                            ? "s"
                            : ""}
                        </p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      {seasonFields.map((season, seasonIndex) => (
                        <SeasonFormSection
                          key={season.id}
                          seasonIndex={seasonIndex}
                          form={form}
                          isOpen={openSeasons.has(seasonIndex)}
                          onToggle={() => toggleSeason(seasonIndex)}
                          onRemove={() => removeSeason(seasonIndex)}
                        />
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </form>
        </Form>
      </section>
    </>
  );
};

export default PodcastNewPage;
