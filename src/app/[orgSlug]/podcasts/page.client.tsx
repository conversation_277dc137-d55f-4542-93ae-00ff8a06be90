"use client";

import { useQuery } from "@tanstack/react-query";
import { CloudUpload, Plus, TriangleDashed } from "lucide-react";
import Image from "next/image";
import type { FC } from "react";
import { OrgAwareLink } from "@/app/organization-aware-link";
import { Query<PERSON>and<PERSON> } from "@/components/query-handler";
import { Button } from "@/components/ui/button";
import {
  Empty,
  EmptyContent,
  EmptyDescription,
  EmptyHeader,
  EmptyMedia,
  EmptyTitle,
} from "@/components/ui/empty";
import {
  Item,
  ItemContent,
  ItemDescription,
  ItemMedia,
  ItemTitle,
} from "@/components/ui/item";
import { Skeleton } from "@/components/ui/skeleton";
import { useTRPC } from "@/server/trpc/client";

export const PodcastClientPage: FC = () => {
  const trpc = useTRPC();

  const podcastQuery = useQuery(trpc.podcasts.get.queryOptions());

  return (
    <>
      <section className="flex items-end justify-between">
        <div className="space-y-4">
          <div className="space-y-2">
            <h1 className="text-4xl font-semibold tracking-tight">Podcasts</h1>
            <p className="text-muted-foreground text-lg">
              Manage your podcasts from a central location.
            </p>
          </div>
        </div>
        <div className="flex gap-4">
          <Button size="sm" asChild>
            <OrgAwareLink href={"/podcasts/new"}>
              <Plus />
              New Podcast
            </OrgAwareLink>
          </Button>
          <Button size="sm" variant="outline" asChild>
            <OrgAwareLink href={"/podcasts/new?action=import"}>
              <CloudUpload />
              Import
            </OrgAwareLink>
          </Button>
        </div>
      </section>
      <section className="py-12">
        <QueryHandler
          query={podcastQuery}
          render={({ data }) => (
            <div className="grid md:grid-cols-3 gap-4">
              {data.map((podcast) => (
                <Item key={podcast.id} variant="outline" asChild>
                  <OrgAwareLink href={`/podcasts/${podcast.id}`}>
                    <ItemMedia variant="image">
                      <Image
                        src={podcast.image || "https://avatar.vercel.sh/nextjs"}
                        alt={podcast.name}
                        width={32}
                        height={32}
                        className="object-cover"
                      />
                    </ItemMedia>
                    <ItemContent className="-space-y-1">
                      <ItemDescription className="text-xs">
                        {podcast.owner}
                      </ItemDescription>
                      <ItemTitle className="line-clamp-1">
                        {podcast.name}
                      </ItemTitle>
                    </ItemContent>
                    <ItemContent className="w-full">
                      <ItemDescription className="text-sm truncate whitespace-nowrap">
                        {podcast.subdomain}
                      </ItemDescription>
                    </ItemContent>
                  </OrgAwareLink>
                </Item>
              ))}
            </div>
          )}
          loading={() => (
            <div className="grid md:grid-cols-3 gap-4 relative">
              {Array.from({ length: 9 }).map((_, i) => (
                <Item
                  key={`podcast-loading-skeleton-${i + 1}`}
                  variant="outline"
                >
                  <ItemMedia variant="image">
                    <Skeleton className="size-10" />
                  </ItemMedia>
                  <ItemContent className="space-y-0.1">
                    <Skeleton className="h-3 w-12" />
                    <Skeleton className="h-4 w-20" />
                  </ItemContent>
                  <ItemContent className="w-full">
                    <Skeleton className="h-4 w-25" />
                  </ItemContent>
                </Item>
              ))}
              <div className="bg-gradient-to-b to-black absolute inset-0" />
            </div>
          )}
          empty={() => (
            <Empty>
              <EmptyHeader>
                <EmptyMedia variant="icon">
                  <TriangleDashed />
                </EmptyMedia>
                <EmptyTitle>No Podcasts Yet</EmptyTitle>
                <EmptyDescription>
                  You haven&apos;t created any podcasts yet. Get started by
                  creating your first podcast.
                </EmptyDescription>
              </EmptyHeader>
              <EmptyContent>
                <div className="flex gap-2">
                  <Button size="sm" asChild>
                    <OrgAwareLink href={"/podcasts/new"}>
                      <Plus />
                      New Podcast
                    </OrgAwareLink>
                  </Button>
                  <Button size="sm" variant="outline" asChild>
                    <OrgAwareLink href={"/podcasts/new?action=import"}>
                      <CloudUpload />
                      Import
                    </OrgAwareLink>
                  </Button>
                </div>
              </EmptyContent>
            </Empty>
          )}
        />
      </section>
    </>
  );
};
