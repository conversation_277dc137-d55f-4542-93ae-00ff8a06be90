import { randomUUID } from "node:crypto";
import fs from "node:fs/promises";
import path from "node:path";
import { $generateHtmlFromNodes } from "@lexical/html";
import { AutoLinkNode, LinkNode } from "@lexical/link";
import { and, eq } from "drizzle-orm";
import { createEditor } from "lexical";
import { z } from "zod";
import { db } from "@/server/db";
import { episode, podcast, season } from "@/server/db/schema";
import { createTRPCRouter, protectedOrgProcedure } from "../init";

const normalizeSubdomain = (raw: string) =>
  raw
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9-]/g, "-")
    .replace(/-{2,}/g, "-")
    .replace(/^-+|-+$/g, "");

// Reserved words you never want as subdomains
const RESERVED = new Set([
  "www",
  "api",
  "admin",
  "assets",
  "static",
  "cdn",
  "mail",
  "smtp",
  "pop",
  "imap",
  "oauth",
  "auth",
]);

// ---- Lexical -> HTML helpers ------------------------------------------------
type SerializedLexical = { root?: { type?: string } } & Record<string, unknown>;
const looksSerializedLexical = (v: unknown): v is SerializedLexical =>
  !!v && typeof v === "object" && (v as any).root?.type === "root";

function serializedLexicalToHTML(serialized: SerializedLexical): string {
  const editor = createEditor({
    namespace: "server-html-export",
    nodes: [LinkNode, AutoLinkNode], // add any custom nodes you use
  });
  const state = editor.parseEditorState(serialized as any);
  editor.setEditorState(state);
  let html = "";
  editor.getEditorState().read(() => {
    html = $generateHtmlFromNodes(editor);
  });
  return html;
}

function normalizeDescriptionToHTML(input: unknown): string | undefined {
  if (typeof input === "string") {
    const s = input.trim();
    if (s.startsWith("{") || s.startsWith("[")) {
      try {
        const parsed = JSON.parse(s);
        if (looksSerializedLexical(parsed))
          return serializedLexicalToHTML(parsed);
      } catch {
        /* ignore */
      }
    }
    return input; // keep as-is (HTML or plain text)
  }
  if (looksSerializedLexical(input)) return serializedLexicalToHTML(input);
  return undefined;
}

const DescriptionField = z.preprocess(
  (v) => normalizeDescriptionToHTML(v),
  z.string().optional(),
);

// ---- Input schema -----------------------------------------------------------
const episodeInput = z.object({
  name: z.string().min(1),
  description: DescriptionField,
  image: z.string().url().optional().or(z.literal("")).optional(),
  audio: z.string().url().optional(),
  duration: z.string().optional(),
});

const seasonInput = z.object({
  name: z.string().min(1),
  description: DescriptionField,
  image: z.string().url().optional().or(z.literal("")).optional(),
  episodes: z.array(episodeInput),
});

const createPodcastInput = z.object({
  name: z.string().min(1),
  subdomain: z.string().optional(),
  description: DescriptionField,
  image: z.string().url().optional().or(z.literal("")).optional(),
  email: z.string().email().optional().or(z.literal("")).optional(),
  owner: z.string().optional(),
  copyright: z.string().optional(),
  seasons: z.array(seasonInput),
});

// ---- File utils -------------------------------------------------------------
const uploadDir = path.join(process.cwd(), "public", "uploads");

function extFromContentType(ct: string | null): string | null {
  if (!ct) return null;
  const t = ct.split(";")[0].trim().toLowerCase();
  switch (t) {
    case "image/png":
      return ".png";
    case "image/jpeg":
    case "image/jpg":
      return ".jpg";
    case "image/webp":
      return ".webp";
    case "image/gif":
      return ".gif";
    case "image/svg+xml":
      return ".svg";
    default:
      return null;
  }
}

async function downloadImageAndStore(
  url?: string | null,
  createdFiles?: string[],
): Promise<string | undefined> {
  if (!url) return undefined;
  if (url.startsWith("/uploads/")) return url; // already local
  if (!/^https?:\/\//i.test(url)) return undefined;

  const res = await fetch(url);
  if (!res.ok) return undefined;

  await fs.mkdir(uploadDir, { recursive: true });

  const ctExt = extFromContentType(res.headers.get("content-type"));
  const urlExt = (() => {
    try {
      return path.extname(new URL(url).pathname);
    } catch {
      return "";
    }
  })();
  const ext = ctExt || urlExt || ".jpg";

  const filename = `${randomUUID()}${ext}`;
  const filePath = path.join(uploadDir, filename);
  const buffer = Buffer.from(await res.arrayBuffer());
  await fs.writeFile(filePath, buffer);

  createdFiles?.push(filePath); // track for cleanup
  return `/uploads/${filename}`;
}

// ---- Router -----------------------------------------------------------------
export const podcastRouter = createTRPCRouter({
  get: protectedOrgProcedure.query(async ({ ctx }) => {
    return db.query.podcast.findMany({
      where: (p, { eq }) => eq(p.organizationId, ctx.org.id),
    });
  }),
  getSingleWithSeasons: protectedOrgProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      return db.query.podcast.findFirst({
        where: (p, { eq, and }) =>
          and(eq(p.id, input.id), eq(p.organizationId, ctx.org.id)),
        with: { seasons: { with: { episodes: true } } },
      });
    }),
  getSingle: protectedOrgProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      return db.query.podcast.findFirst({
        where: (p, { eq, and }) =>
          and(eq(p.id, input.id), eq(p.organizationId, ctx.org.id)),
      });
    }),
  delete: protectedOrgProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      await db
        .delete(podcast)
        .where(
          and(eq(podcast.id, input.id), eq(podcast.organizationId, ctx.org.id)),
        );
    }),
  update: protectedOrgProcedure
    .input(
      z.object({
        id: z.string(),
        data: z.object({
          name: z.string().optional(),
          subdomain: z.string().optional(),
        }),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      await db
        .update(podcast)
        .set({ name: input.data.name, subdomain: input.data.subdomain })
        .where(
          and(eq(podcast.id, input.id), eq(podcast.organizationId, ctx.org.id)),
        )
        .returning({ id: podcast.id });
    }),
  subdomainAvailability: protectedOrgProcedure
    .input(
      z.object({
        subdomain: z.string().min(3).max(64),
        excludeId: z.string().optional(),
      }),
    )
    .query(async ({ input }) => {
      const normalized = normalizeSubdomain(input.subdomain);

      if (normalized.length < 3 || normalized.length > 64) {
        return {
          available: false,
          normalized,
          reason: "length" as const,
          suggestions: [],
        };
      }

      if (RESERVED.has(normalized)) {
        return {
          available: false,
          normalized,
          reason: "reserved" as const,
          suggestions: [],
        };
      }

      const taken = await db.query.podcast.findFirst({
        where: (p, { eq, and, ne }) =>
          and(
            eq(p.subdomain, normalized),
            input.excludeId ? ne(p.id, input.excludeId) : undefined,
          ),
        columns: { id: true },
      });

      if (taken) {
        // Simple suggestion generator: try appending digits
        const suggestions: string[] = [];
        for (let i = 1; i <= 5; i++) {
          const trySlug = `${normalized}-${i}`.slice(0, 64);
          // Ideally check DB for each, but keep it light client-side; the client can probe if needed
          suggestions.push(trySlug);
        }
        return {
          available: false,
          normalized,
          reason: "taken" as const,
          suggestions,
        };
      }

      return {
        available: true,
        normalized,
        reason: "available" as const,
        suggestions: [],
      };
    }),
  create: protectedOrgProcedure
    .input(createPodcastInput)
    .mutation(async ({ input, ctx }) => {
      const created = {
        podcastId: null as number | null,
        seasonIds: [] as number[],
        episodeIds: [] as number[],
        files: [] as string[], // absolute paths for cleanup
      };

      try {
        const podcastImage = await downloadImageAndStore(
          input.image,
          created.files,
        );

        const [{ id: podcastId }] = await db
          .insert(podcast)
          .values({
            name: input.name,
            subdomain: input.subdomain,
            description: input.description, // already HTML via preprocess
            image: podcastImage,
            email: input.email || undefined,
            owner: input.owner,
            copyright: input.copyright,
            organizationId: ctx.org.id,
          })
          .returning({ id: podcast.id });

        created.podcastId = podcastId;

        for (const s of input.seasons) {
          const seasonImage = await downloadImageAndStore(
            s.image,
            created.files,
          );

          const [{ id: seasonId }] = await db
            .insert(season)
            .values({
              name: s.name,
              description: s.description,
              image: seasonImage,
              podcastId,
            })
            .returning({ id: season.id });

          created.seasonIds.push(seasonId);

          for (const e of s.episodes) {
            const episodeImage = await downloadImageAndStore(
              e.image,
              created.files,
            );

            const [{ id: epId }] = await db
              .insert(episode)
              .values({
                name: e.name,
                description: e.description,
                image: episodeImage,
                audio: e.audio,
                duration: e.duration,
                seasonId,
              })
              .returning({ id: episode.id });

            created.episodeIds.push(epId);
          }
        }

        return { success: true, id: created.podcastId };
      } catch (err) {
        // ---- manual rollback (best effort) ----
        try {
          // delete episodes first
          for (const id of created.episodeIds) {
            await db.delete(episode).where(eq(episode.id, id));
          }
          // then seasons
          for (const id of created.seasonIds) {
            await db.delete(season).where(eq(season.id, id));
          }
          // then podcast
          if (created.podcastId !== null) {
            await db.delete(podcast).where(eq(podcast.id, created.podcastId));
          }
        } catch (cleanupErr) {
          console.error("Cleanup failed:", cleanupErr);
        }
        // delete any files we wrote
        await Promise.allSettled(
          created.files.map(async (absPath) => {
            try {
              await fs.unlink(absPath);
            } catch {}
          }),
        );

        console.error("Create podcast failed:", err);
        throw err;
      }
    }),
});
