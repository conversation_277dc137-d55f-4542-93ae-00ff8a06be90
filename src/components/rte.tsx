/** biome-ignore-all lint/suspicious/noExplicitAny: NEVER CHANGE THIS FILE */
/** biome-ignore-all lint/style/noNonNullAssertion: NEVER CHANGE THIS FILE */
/** biome-ignore-all lint/correctness/useExhaustiveDependencies: NEVER CHANGE THIS FILE */

import { $generateNodesFromDOM } from "@lexical/html";
import {
  $isLinkNode,
  AutoLinkNode,
  LinkNode,
  TOGGLE_LINK_COMMAND,
} from "@lexical/link";
import { AutoLinkPlugin } from "@lexical/react/LexicalAutoLinkPlugin";
import {
  type InitialConfigType,
  LexicalComposer,
} from "@lexical/react/LexicalComposer";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { LinkPlugin } from "@lexical/react/LexicalLinkPlugin";
import { OnChangePlugin } from "@lexical/react/LexicalOnChangePlugin";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import {
  $createParagraphNode,
  $getNearestNodeFromDOMNode,
  $getRoot,
  $getSelection,
  $isDecoratorNode,
  $isElementNode,
  $isRangeSelection,
  $isTextNode,
  COMMAND_PRIORITY_NORMAL,
  type EditorState,
  KEY_MODIFIER_COMMAND,
  type SerializedEditorState,
} from "lexical";
import {
  type FC,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";

// shadcn/ui
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { editorTheme } from "@/lib/rte-theme";

const editorConfig: InitialConfigType = {
  namespace: "Editor",
  theme: editorTheme,
  nodes: [LinkNode, AutoLinkNode],
  onError: (error: Error) => {
    console.error(error);
  },
};

// --- Linking helpers ---------------------------------------------------------

const URL_REGEX = /(?:https?:\/\/|www\.)[^\s<>"']+[^\s<>"'.,;:!?)]/i;
const EMAIL_REGEX = /[\w.+-]+@[\w.-]+\.[A-Za-z]{2,}/;

const MATCHERS = [
  (text: string) => {
    const match = URL_REGEX.exec(text);
    if (!match) return null;
    const full = match[0];
    const url = full.startsWith("http") ? full : `https://${full}`;
    return { index: match.index, length: full.length, text: full, url };
  },
  (text: string) => {
    const match = EMAIL_REGEX.exec(text);
    if (!match) return null;
    const full = match[0];
    return {
      index: match.index,
      length: full.length,
      text: full,
      url: `mailto:${full}`,
    };
  },
];

function validateUrl(url: string) {
  try {
    if (url.startsWith("mailto:")) return true;
    const parsed =
      url.startsWith("http://") || url.startsWith("https://")
        ? new URL(url)
        : new URL(`https://${url}`);
    return !!parsed.hostname;
  } catch {
    return false;
  }
}

// --- Floating link editor (inline panel) ------------------------------------

export function FloatingLinkEditorPlugin() {
  const [editor] = useLexicalComposerContext();

  const [open, setOpen] = useState(false);
  const [url, setUrl] = useState("");
  const [hasLink, setHasLink] = useState(false);

  const [targetRect, setTargetRect] = useState<DOMRect | null>(null);
  const panelRef = useRef<HTMLDivElement>(null);

  const [pos, setPos] = useState<{
    left: number;
    top: number;
    flipBelow: boolean;
    arrowLeft: number;
  } | null>(null);

  // keep link state in sync
  useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        const sel = $getSelection();
        let currentUrl = "";
        let inside = false;
        if ($isRangeSelection(sel)) {
          let n = sel.anchor.getNode();
          while (n) {
            if ($isLinkNode(n)) {
              currentUrl = n.getURL() ?? "";
              inside = true;
              break;
            }
            n = n.getParent();
          }
        }
        setHasLink(inside);
        if (!open) setUrl(currentUrl);
      });
    });
  }, [editor, open]);

  // Cmd/Ctrl+K opens and anchors to selection/link
  useEffect(() => {
    return editor.registerCommand(
      KEY_MODIFIER_COMMAND,
      (evt: KeyboardEvent) => {
        if ((evt.metaKey || evt.ctrlKey) && evt.key.toLowerCase() === "k") {
          evt.preventDefault();
          anchorFromSelection();
          setOpen(true);
          return true;
        }
        return false;
      },
      COMMAND_PRIORITY_NORMAL,
    );
  }, [editor]);

  // Click on link -> prevent navigation, select it, open, and anchor to it
  useEffect(() => {
    const root = editor.getRootElement();
    if (!root) return;

    const onClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement | null;
      const a = target?.closest("a");
      if (!a) return;

      e.preventDefault();
      e.stopPropagation();

      editor.update(() => {
        const node = $getNearestNodeFromDOMNode(a);
        if ($isLinkNode(node)) node.selectStart();
      });

      setTargetRect(a.getBoundingClientRect());
      setUrl(a.getAttribute("href") ?? "");
      setOpen(true);
    };

    root.addEventListener("click", onClick, true);
    return () => root.removeEventListener("click", onClick, true);
  }, [editor]);

  // measure & position AFTER panel mounts (fixes "doesn't open" issue)
  useLayoutEffect(() => {
    if (!open) return;
    if (!panelRef.current) return;

    const compute = () => {
      const container =
        (panelRef.current!.offsetParent as HTMLElement) ?? document.body;
      const containerRect = container.getBoundingClientRect();
      const panelW = panelRef.current!.offsetWidth;
      const panelH = panelRef.current!.offsetHeight;
      const gap = 8;
      const margin = 8;

      // derive rect: prefer current targetRect; else from selection
      let rect = targetRect;
      if (!rect) {
        rect = rangeRectFromSelection();
        setTargetRect(rect ?? null);
        if (!rect) return;
      }

      const centerX = rect.left + rect.width / 2;

      // try above first; flip if not enough room
      let topPx = rect.top - gap - panelH;
      let flipBelow = false;
      if (topPx < containerRect.top + margin) {
        topPx = rect.bottom + gap;
        flipBelow = true;
      }

      let leftPx = centerX - panelW / 2;
      const minLeft = containerRect.left + margin;
      const maxLeft = containerRect.right - margin - panelW;
      if (leftPx < minLeft) leftPx = minLeft;
      if (leftPx > maxLeft) leftPx = maxLeft;

      const left = leftPx - containerRect.left;
      const top = topPx - containerRect.top;

      const arrowLeft = Math.max(12, Math.min(panelW - 12, centerX - leftPx));

      setPos({ left, top, flipBelow, arrowLeft });
    };

    // compute on next frame so layout is stable
    const id = requestAnimationFrame(compute);
    return () => cancelAnimationFrame(id);
  }, [open, targetRect]);

  // keep position in sync on scroll/resize while open
  useEffect(() => {
    if (!open) return;
    const handler = () => {
      // refresh target from selection (in case the link moved)
      setTargetRect(rangeRectFromSelection());
    };
    window.addEventListener("scroll", handler, true);
    window.addEventListener("resize", handler);
    return () => {
      window.removeEventListener("scroll", handler, true);
      window.removeEventListener("resize", handler);
    };
  }, [open]);

  function anchorFromSelection() {
    const r = rangeRectFromSelection();
    setTargetRect(r ?? null);
  }

  function rangeRectFromSelection(): DOMRect | null {
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0) return null;
    const r = sel.getRangeAt(0);
    const rect = r.getBoundingClientRect();
    if (rect.width === 0 && rect.height === 0) return null;
    return rect;
  }

  const apply = () => {
    const u = url.trim();
    if (!validateUrl(u)) return;
    editor.dispatchCommand(TOGGLE_LINK_COMMAND, u);
    setOpen(false);
  };
  const remove = () => {
    editor.dispatchCommand(TOGGLE_LINK_COMMAND, null);
    setOpen(false);
  };

  const disabledApply = useMemo(() => !validateUrl(url.trim()), [url]);

  // Render the panel whenever open. If `pos` isn't ready yet, keep it hidden;
  // useLayoutEffect will measure, then we set `pos` and show it.
  if (!open) return null;

  return (
    <button
      ref={panelRef}
      type="button"
      className="absolute z-50 rounded-md border bg-popover p-2 shadow-md"
      style={{
        left: pos ? pos.left : 0,
        top: pos ? pos.top : 0,
        visibility: pos ? "visible" : "hidden",
      }}
      onMouseDown={(e) => e.stopPropagation()}
    >
      <div className="flex items-center gap-2">
        <Input
          className="h-8 w-72"
          placeholder="https://example.com"
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              e.preventDefault();
              apply();
            }
          }}
          autoFocus
        />
        <Button size="sm" onClick={apply} disabled={disabledApply}>
          Apply
        </Button>
        <Button
          size="sm"
          variant="destructive"
          onClick={remove}
          disabled={!hasLink}
        >
          Remove
        </Button>
      </div>

      {/* arrow */}
      <div
        className={[
          "absolute h-2 w-2 rotate-45 border-l border-t bg-popover",
          pos?.flipBelow ? "top-0 -translate-y-1/2" : "top-full -translate-y-1",
        ].join(" ")}
        style={{ left: pos?.arrowLeft ?? 16 }}
      />
    </button>
  );
}

// --- HTML import -------------------------------------------------------------

function normalizePlainTextToHTML(input: string) {
  const looksLikeHTML = /<[^>]+>/.test(input);
  if (looksLikeHTML) return input;

  const paragraphs = input.split(/\n{2,}/);
  const htmlParas = paragraphs
    .map((p) =>
      p
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/\n/g, "<br/>"),
    )
    .map((p) => `<p>${p}</p>`)
    .join("");
  return htmlParas || "<p></p>";
}

function HtmlImportPlugin({ html }: { html?: string }) {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    if (typeof html !== "string") return;

    editor.update(() => {
      const htmlToParse = normalizePlainTextToHTML(html);
      const parser = new DOMParser();
      const dom = parser.parseFromString(htmlToParse, "text/html");
      const nodes = $generateNodesFromDOM(editor, dom);

      const root = $getRoot();
      root.select();
      root.clear();

      const topLevel: any[] = [];
      let textBuffer: any[] = [];

      const flushTextBuffer = () => {
        if (textBuffer.length) {
          const p = $createParagraphNode();
          p.append(...textBuffer);
          topLevel.push(p);
          textBuffer = [];
        }
      };

      for (const n of nodes) {
        if ($isElementNode(n) || $isDecoratorNode(n)) {
          flushTextBuffer();
          topLevel.push(n);
        } else if ($isTextNode(n)) {
          textBuffer.push(n);
        } else {
          textBuffer.push(n);
        }
      }
      flushTextBuffer();

      root.append(...topLevel);
      root.selectEnd();
    });
  }, [editor, html]);

  return null;
}

// --- Component ---------------------------------------------------------------

interface IRTEProps {
  html?: string;
  editorState?: EditorState;
  editorSerializedState?: SerializedEditorState;
  onChange?: (editorState: EditorState) => void;
  onSerializedChange?: (editorSerializedState: SerializedEditorState) => void;
}

export const RTE: FC<IRTEProps> = ({
  html,
  editorState,
  editorSerializedState,
  onChange,
  onSerializedChange,
}) => {
  return (
    <div className="relative selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border bg-transparent focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border-input p-3 rounded-md">
      <LexicalComposer
        initialConfig={{
          ...editorConfig,
          ...(editorState ? { editorState } : {}),
          ...(editorSerializedState
            ? { editorState: editorSerializedState }
            : {}),
        }}
      >
        <RichTextPlugin
          contentEditable={
            <ContentEditable
              className="whitespace-pre-wrap break-words focus:outline-none text-sm"
              aria-placeholder="Enter some text..."
              placeholder={
                <div className="text-muted-foreground absolute top-4 text-sm pointer-events-none">
                  Enter some text...
                </div>
              }
            />
          }
          ErrorBoundary={LexicalErrorBoundary}
        />

        <OnChangePlugin
          ignoreSelectionChange
          onChange={(state) => {
            onChange?.(state);
            onSerializedChange?.(state.toJSON());
          }}
        />

        <HistoryPlugin />

        {/* 🔗 Link features */}
        <LinkPlugin validateUrl={validateUrl} />
        <AutoLinkPlugin matchers={MATCHERS} />
        <FloatingLinkEditorPlugin />

        {/* HTML import */}
        <HtmlImportPlugin html={html} />
      </LexicalComposer>
    </div>
  );
};
