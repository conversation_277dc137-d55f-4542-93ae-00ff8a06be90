import type { <PERSON> } from "react";
import { cn } from "@/lib/utils";
import { Badge } from "./ui/badge";

interface IPlanBadgeProps extends React.ComponentProps<"span"> {
  plan: "free" | "pro" | "business";
}

const gradients = {
  free: "",
  pro: "linear-gradient(45deg, #FF512F, #DD2476)",
  business: "linear-gradient(45deg, #24C6DC, #514A9D)",
};

export const PlanBadge: FC<IPlanBadgeProps> = ({
  plan,
  className,
  ...props
}) => {
  return (
    <Badge
      variant="outline"
      className={cn("!text-xs capitalize rounded-full", className)}
      style={{ background: gradients[plan] }}
      {...props}
    >
      {plan}
    </Badge>
  );
};
