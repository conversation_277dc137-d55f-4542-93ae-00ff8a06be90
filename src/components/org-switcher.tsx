"use client";

import { OrganizationSwitcher } from "@daveyplate/better-auth-ui";
import { ChevronsDownUp, ChevronsUpDown } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { type FC, useEffect, useMemo, useRef } from "react";
import { OrgAwareLink } from "@/app/organization-aware-link";
import { authClient } from "@/auth/client";
import { Button, buttonVariants } from "@/components/ui/button";
import { PlanBadge } from "./plan-badge";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";

type Org = { id: string; slug: string; logo?: string | null | undefined };

export const OrgSwitcher: FC = () => {
  const params = useParams();
  const router = useRouter();

  const { data: activeOrgData, isPending: activeLoading } =
    authClient.useActiveOrganization();
  const { data: allOrgsData, isPending: orgsLoading } =
    authClient.useListOrganizations?.() ?? {
      data: undefined,
      isLoading: true,
    };

  const activeOrg = activeOrgData as Org | null | undefined;
  const allOrgs = (allOrgsData as Org[] | undefined) ?? [];

  const syncingRef = useRef(false);

  const setActiveOrg = async (organizationId: string) =>
    authClient.organization.setActive({
      organizationId,
    });

  // normalize [orgSlug]
  const paramSlug = useMemo(() => {
    const raw = (params as Record<string, unknown>)?.orgSlug;
    if (typeof raw === "string") return raw;
    if (Array.isArray(raw) && raw.length) return String(raw[0]);
    return undefined;
  }, [params]);

  const ready = !activeLoading && !orgsLoading;

  const desiredFromUrl: Org | undefined = useMemo(() => {
    if (!ready) return undefined;
    if (!paramSlug) return undefined;
    return allOrgs.find((o) => o.slug === paramSlug);
  }, [ready, paramSlug, allOrgs]);

  // Sync state from URL → active org
  // biome-ignore lint/correctness/useExhaustiveDependencies: run when key deps change
  useEffect(() => {
    if (!ready) return;
    if (paramSlug === undefined) return;
    if (syncingRef.current) return;

    const currentSlug = activeOrg?.slug;

    // If no slug matches, choose a fallback (prefer current, else first org)
    if (!desiredFromUrl) {
      const fallbackSlug = currentSlug ?? allOrgs[0]?.slug;
      if (!fallbackSlug) return; // no orgs — nothing to do

      syncingRef.current = true;
      (async () => {
        const fallbackOrg = allOrgs.find((o) => o.slug === fallbackSlug);
        if (fallbackOrg && fallbackOrg.id !== activeOrg?.id) {
          await setActiveOrg(fallbackOrg.id);
        }
        if (paramSlug !== fallbackSlug) {
          router.replace(`/${fallbackSlug}`);
        }
      })().finally(() => {
        syncingRef.current = false;
      });
      return;
    }

    // Org requested and different from current
    if (desiredFromUrl.slug !== currentSlug) {
      syncingRef.current = true;
      (async () => {
        await setActiveOrg(desiredFromUrl.id);
        if (paramSlug !== desiredFromUrl.slug)
          router.replace(`/${desiredFromUrl.slug}`);
      })().finally(() => {
        syncingRef.current = false;
      });
    }
  }, [ready, paramSlug, desiredFromUrl, activeOrg, allOrgs, router]);

  // Keep URL in sync when active org changes elsewhere
  useEffect(() => {
    if (!ready) return;
    if (syncingRef.current) return;

    const currentSlug = activeOrg?.slug;
    if (!currentSlug) return; // no orgs — nothing to do
    if (paramSlug !== currentSlug) {
      syncingRef.current = true;
      router.replace(`/${currentSlug}`);
      syncingRef.current = false;
    }
  }, [ready, activeOrg, paramSlug, router]);

  // If there are no orgs, render nothing (or you could render a placeholder)
  if (!allOrgs.length) return null;

  return (
    <div className="flex items-center gap-x-8 w-[256px]">
      <div className="flex items-center gap-x-2">
        <OrgAwareLink href="/" className="flex items-center gap-x-2">
          <Avatar className="size-7">
            <AvatarImage src={activeOrg?.logo || ""} />
            <AvatarFallback>{activeOrg?.slug?.[0]}</AvatarFallback>
          </Avatar>
          <div className="text-sm">{activeOrg?.slug}</div>
        </OrgAwareLink>
        <PlanBadge plan="pro" />
      </div>
      <OrganizationSwitcher
        slug={activeOrg?.slug}
        trigger={
          <Button variant="ghost" size="icon" className="h-6 w-6 flex-shrink-0">
            <ChevronsUpDown />
            <span className="sr-only">Switch Organization</span>
          </Button>
        }
        hidePersonal
        align="start"
        className={buttonVariants({
          variant: "ghost",
          className: "bg-transparent text-foreground",
        })}
        onSetActive={(org) => {
          if (!org) return; // with hidePersonal, org should always exist
          const nextSlug = org.slug;
          syncingRef.current = true;
          (async () => {
            await setActiveOrg(org.id);
            router.push(`/${nextSlug}`);
          })().finally(() => {
            syncingRef.current = false;
          });
        }}
      />
    </div>
  );
};
